<!DOCTYPE html>
<html>
<head>
    <title>ESP32 Color Matcher - Test Page</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .container { max-width: 800px; margin: 0 auto; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        button { padding: 10px 20px; margin: 5px; background: #007bff; color: white; border: none; border-radius: 3px; cursor: pointer; }
        button:hover { background: #0056b3; }
        .result { margin: 10px 0; padding: 10px; background: #f8f9fa; border-radius: 3px; }
        .error { background: #f8d7da; color: #721c24; }
        .success { background: #d4edda; color: #155724; }
    </style>
</head>
<body>
    <div class="container">
        <h1>ESP32 Color Matcher - Test Page</h1>
        
        <div class="section">
            <h2>Device Status</h2>
            <button onclick="checkStatus()">Check Status</button>
            <div id="statusResult" class="result"></div>
        </div>
        
        <div class="section">
            <h2>Settings</h2>
            <button onclick="getSettings()">Get Settings</button>
            <div id="settingsResult" class="result"></div>
        </div>
        
        <div class="section">
            <h2>Color Scan</h2>
            <button onclick="performScan()">Perform Scan</button>
            <div id="scanResult" class="result"></div>
        </div>
        
        <div class="section">
            <h2>Saved Samples</h2>
            <button onclick="getSamples()">Get Samples</button>
            <div id="samplesResult" class="result"></div>
        </div>
        
        <div class="section">
            <h2>Raw Sensor Data</h2>
            <button onclick="getRawData()">Get Raw Data</button>
            <div id="rawResult" class="result"></div>
        </div>
    </div>

    <script>
        async function makeRequest(url, method = 'GET', body = null) {
            try {
                const options = {
                    method: method,
                    headers: {
                        'Content-Type': 'application/json',
                    }
                };
                
                if (body) {
                    options.body = JSON.stringify(body);
                }
                
                const response = await fetch(url, options);
                const data = await response.json();
                return { success: true, data: data };
            } catch (error) {
                return { success: false, error: error.message };
            }
        }
        
        function displayResult(elementId, result) {
            const element = document.getElementById(elementId);
            if (result.success) {
                element.className = 'result success';
                element.innerHTML = '<pre>' + JSON.stringify(result.data, null, 2) + '</pre>';
            } else {
                element.className = 'result error';
                element.innerHTML = 'Error: ' + result.error;
            }
        }
        
        async function checkStatus() {
            const result = await makeRequest('/status');
            displayResult('statusResult', result);
        }
        
        async function getSettings() {
            const result = await makeRequest('/settings');
            displayResult('settingsResult', result);
        }
        
        async function performScan() {
            const result = await makeRequest('/scan', 'POST', {});
            displayResult('scanResult', result);
        }
        
        async function getSamples() {
            const result = await makeRequest('/samples');
            displayResult('samplesResult', result);
        }
        
        async function getRawData() {
            const result = await makeRequest('/raw-sensor-data');
            displayResult('rawResult', result);
        }
        
        // Auto-load status on page load
        window.onload = function() {
            checkStatus();
        };
    </script>
</body>
</html>
