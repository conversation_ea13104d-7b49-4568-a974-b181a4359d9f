#include <WiFi.h>
#include <WebServer.h>
#include <LittleFS.h>
#include <Preferences.h>
#include <HTTPClient.h>
#include <ArduinoJson.h>
#include <DFRobot_TCS3430.h>
#include <Adafruit_NeoPixel.h>
#include <esp_task_wdt.h>
#include <vector>
#include "config.h"
#include "logging.h"
#include "cie1931.h"
#include "dynamic_sensor.h"
#include "AdvancedTCS3430Calibration.h"

// ============================================================================
// TYPE DEFINITIONS AND STRUCTURES
// ============================================================================

struct ColorSample {
  uint8_t r, g, b;
  uint32_t timestamp;
  char paintName[SAMPLE_NAME_LENGTH];
  char paintCode[SAMPLE_CODE_LENGTH];
  float lrv;
};

// ============================================================================
// GLOBAL OBJECTS AND CONFIGURATION
// ============================================================================

// WiFi credentials
const char* ssid = WIFI_SSID;
const char* password = WIFI_PASSWORD;

// Global objects
DFRobot_TCS3430 tcs3430;
Adafruit_NeoPixel rgbLed(1, RGB_LED_PIN, NEO_GRB + NEO_KHZ800);
WebServer server(WEB_SERVER_PORT);
Preferences preferences;

// Encapsulated management systems
DynamicSensorManager* dynamicSensor = nullptr;
AdvancedTCS3430Calibration* advancedCalibration = nullptr;

// Logger static member definitions
unsigned long Logger::startTime = 0;
bool Logger::initialized = false;

// Sample storage
ColorSample samples[MAX_SAMPLES];
uint8_t sampleCount = 0;
uint8_t sampleIndex = 0;

// System state
bool isScanning = false;
bool isCalibrated = false;
uint8_t currentBrightness = DEFAULT_BRIGHTNESS;

// ============================================================================
// FUNCTION DECLARATIONS
// ============================================================================

// Core system functions
bool initializeSensor();
void connectToWiFi();
void setupWebServer();
void loadSettings();
void saveSettings();
void loadSamples();
void saveSamples();

// LED control functions
void setLEDColor(uint8_t r, uint8_t g, uint8_t b, uint8_t brightness);
void turnOffLED();
void initializePWM();
void setIlluminationBrightness(uint8_t brightness);
void turnOffIllumination();

// Web handlers
void handleScan();
void handleEnhancedScan();
void handleSaveSample();
void handleSavedSamples();
void handleDeleteSample();
void handleClearAllSamples();
void handleSettings();
void handleGetSettings();
void handleStatus();
void handleBrightness();
void handleRawSensorData();

// Utility functions
void handleCORSHeaders();
void handleCORSPreflight();
void sendJsonResponse(int statusCode, const JsonDocument& doc);
void convertSensorToRGB(uint16_t rawR, uint16_t rawG, uint16_t rawB, uint16_t rawIR, uint8_t& r, uint8_t& g, uint8_t& b);

// ============================================================================
// MAIN SETUP FUNCTION
// ============================================================================

void setup() {
  // Initialize serial communication
  Serial.begin(SERIAL_BAUD_RATE);
  Serial.flush();
  delay(100);

  // Early diagnostic output
  Serial.println("\n=== ESP32-S3 COLOR MATCHER BOOT SEQUENCE ===");
  Serial.printf("Free heap at start: %u bytes\n", ESP.getFreeHeap());
  Serial.flush();

  // Feed watchdog early
  esp_task_wdt_reset();
  delay(100);

  // Initialize logging system
  Logger::init();

  LOG_SYS_INFO("=== SCROFANI COLOR MATCHER STARTING ===");
  LOG_SYS_INFO("Firmware: %s", FIRMWARE_VERSION);
  LOG_SYS_INFO("Hardware: %s", HARDWARE_VERSION);
  LOG_SYS_INFO("Build: %s %s", BUILD_DATE, BUILD_TIME);

  // Initialize RGB LED and power control
  rgbLed.begin();
  rgbLed.setBrightness(DEFAULT_BRIGHTNESS);
  rgbLed.show();
  pinMode(LDO2_POWER_PIN, OUTPUT);
  digitalWrite(LDO2_POWER_PIN, HIGH);

  // Initialize PWM for illumination LED
  initializePWM();

  // Initialize I2C
  Wire.begin(I2C_SDA_PIN, I2C_SCL_PIN);
  Wire.setClock(I2C_CLOCK_SPEED);

  // Initialize TCS3430 sensor
  if (!initializeSensor()) {
    LOG_SENSOR_ERROR("=== SENSOR INITIALIZATION FAILED ===");
    for (int i = 0; i < 10; i++) {
      setLEDColor(255, 0, 0, 255);
      delay(200);
      setLEDColor(0, 0, 0, 0);
      delay(200);
      esp_task_wdt_reset();
    }
    ESP.restart();
  }

  // Initialize dynamic sensor management
  dynamicSensor = new DynamicSensorManager(&tcs3430);
  if (!dynamicSensor->initialize()) {
    LOG_SENSOR_WARN("Dynamic sensor management initialization failed");
    delete dynamicSensor;
    dynamicSensor = nullptr;
  }

  // Initialize advanced calibration system
  advancedCalibration = new AdvancedTCS3430Calibration(&tcs3430, ILLUMINATION_LED_PIN);
  if (!advancedCalibration->initialize()) {
    LOG_SENSOR_ERROR("Advanced calibration system initialization failed");
    delete advancedCalibration;
    advancedCalibration = nullptr;
  }

  // Initialize filesystem
  if (!LittleFS.begin(true)) {
    LOG_SYS_ERROR("LittleFS mount failed");
  }

  // Initialize preferences
  preferences.begin(PREF_NAMESPACE, false);

  // Load settings and samples
  loadSettings();
  loadSamples();

  // Connect to WiFi
  connectToWiFi();

  // Setup web server
  setupWebServer();

  LOG_SYS_INFO("=== SYSTEM INITIALIZATION COMPLETED ===");
  Logger::logMemoryUsage("System initialization complete");
  LOG_SYS_INFO("Color matcher ready for operation");
}

// ============================================================================
// MAIN LOOP FUNCTION
// ============================================================================

void loop() {
  static unsigned long lastWatchdogFeed = 0;

  // Feed watchdog every 1 second
  if (millis() - lastWatchdogFeed > 1000) {
    esp_task_wdt_reset();
    lastWatchdogFeed = millis();
  }

  server.handleClient();

  // Periodically optimize sensor settings if dynamic sensor is available
  static unsigned long lastOptimization = 0;
  if (dynamicSensor && dynamicSensor->isInitialized() &&
      millis() - lastOptimization > 5000) {
    dynamicSensor->optimizeSensorSettings();
    lastOptimization = millis();
  }

  delay(LOOP_DELAY_MS);
}

// ============================================================================
// SENSOR INITIALIZATION
// ============================================================================

bool initializeSensor() {
  LOG_PERF_START();
  LOG_SENSOR_INFO("Attempting TCS3430 initialization");

  int initAttempts = 0;
  const int maxInitAttempts = 5;

  while (!tcs3430.begin() && initAttempts < maxInitAttempts) {
    initAttempts++;
    LOG_SENSOR_WARN("TCS3430 initialization attempt %d/%d failed", initAttempts, maxInitAttempts);
    delay(1000);
  }

  if (initAttempts >= maxInitAttempts) {
    LOG_SENSOR_ERROR("TCS3430 initialization failed after %d attempts", maxInitAttempts);
    return false;
  }

  LOG_SENSOR_INFO("TCS3430 initialized after %d attempt(s)", initAttempts + 1);

  // Configure sensor with optimal settings
  tcs3430.setIntegrationTime(DEFAULT_ATIME);
  tcs3430.setALSGain(DEFAULT_AGAIN);
  tcs3430.setWaitTime(DEFAULT_WAIT_TIME);

  delay(SENSOR_STABILIZE_MS);
  LOG_PERF_END("TCS3430 initialization");
  return true;
}

// ============================================================================
// WIFI CONNECTION
// ============================================================================

void connectToWiFi() {
  LOG_PERF_START();
  LOG_NET_INFO("Starting WiFi connection to SSID: %s", ssid);

  WiFi.disconnect(true);
  delay(1000);
  WiFi.mode(WIFI_STA);
  WiFi.setAutoConnect(false);
  WiFi.setAutoReconnect(true);

  #if USE_STATIC_IP
  IPAddress staticIP, gateway, subnet, dns1, dns2;
  staticIP.fromString(STATIC_IP_ADDRESS);
  gateway.fromString(STATIC_GATEWAY);
  subnet.fromString(STATIC_SUBNET);
  dns1.fromString(STATIC_DNS1);
  dns2.fromString(STATIC_DNS2);
  if (WiFi.config(staticIP, gateway, subnet, dns1, dns2)) {
    LOG_NET_INFO("Static IP configured: %s", STATIC_IP_ADDRESS);
  }
  #endif

  WiFi.setHostname("ColorMatcher");

  const int maxRetries = 3;
  const int maxAttemptsPerRetry = 20;

  for (int retry = 0; retry < maxRetries; retry++) {
    LOG_NET_INFO("WiFi connection retry %d/%d", retry + 1, maxRetries);
    WiFi.begin(ssid, password);

    int attempts = 0;
    while (WiFi.status() != WL_CONNECTED && attempts < maxAttemptsPerRetry) {
      delay(2000);
      attempts++;
      esp_task_wdt_reset();
    }

    if (WiFi.status() == WL_CONNECTED) {
      break;
    }

    if (retry < maxRetries - 1) {
      WiFi.disconnect();
      delay(3000);
    }
  }

  if (WiFi.status() == WL_CONNECTED) {
    LOG_NET_INFO("WiFi connected - IP: %s, RSSI: %d dBm", 
                 WiFi.localIP().toString().c_str(), WiFi.RSSI());
  } else {
    LOG_NET_INFO("Creating Access Point as fallback...");
    WiFi.mode(WIFI_AP);
    if (WiFi.softAP("ESP32-ColorMatcher", "colormatching123")) {
      LOG_NET_INFO("AP created - IP: %s", WiFi.softAPIP().toString().c_str());
    }
  }

  LOG_PERF_END("WiFi connection");
}

// ============================================================================
// WEB SERVER SETUP
// ============================================================================

void setupWebServer() {
  LOG_PERF_START();
  LOG_WEB_INFO("Configuring web server endpoints");

  // Handle OPTIONS requests for CORS preflight
  server.on("/scan", HTTP_OPTIONS, handleCORSPreflight);
  server.on("/enhanced-scan", HTTP_OPTIONS, handleCORSPreflight);
  server.on("/save", HTTP_OPTIONS, handleCORSPreflight);
  server.on("/samples", HTTP_OPTIONS, handleCORSPreflight);
  server.on("/delete", HTTP_OPTIONS, handleCORSPreflight);
  server.on("/samples/clear", HTTP_OPTIONS, handleCORSPreflight);
  server.on("/settings", HTTP_OPTIONS, handleCORSPreflight);
  server.on("/status", HTTP_OPTIONS, handleCORSPreflight);
  server.on("/brightness", HTTP_OPTIONS, handleCORSPreflight);

  // API endpoints
  server.on("/scan", HTTP_POST, []() { handleCORSHeaders(); handleScan(); });
  server.on("/enhanced-scan", HTTP_POST, []() { handleCORSHeaders(); handleEnhancedScan(); });
  server.on("/save", HTTP_POST, []() { handleCORSHeaders(); handleSaveSample(); });
  server.on("/samples", HTTP_GET, []() { handleCORSHeaders(); handleSavedSamples(); });
  server.on("/delete", HTTP_POST, []() { handleCORSHeaders(); handleDeleteSample(); });
  server.on("/samples/clear", HTTP_POST, []() { handleCORSHeaders(); handleClearAllSamples(); });
  server.on("/settings", HTTP_POST, []() { handleCORSHeaders(); handleSettings(); });
  server.on("/settings", HTTP_GET, []() { handleCORSHeaders(); handleGetSettings(); });
  server.on("/status", HTTP_GET, []() { handleCORSHeaders(); handleStatus(); });
  server.on("/brightness", HTTP_POST, []() { handleCORSHeaders(); handleBrightness(); });
  server.on("/raw", HTTP_GET, []() { handleCORSHeaders(); handleRawSensorData(); });

  // Advanced calibration endpoints
  if (advancedCalibration) {
    server.on("/advanced-calibration/white", HTTP_POST, []() {
      handleCORSHeaders();
      JsonDocument response;
      bool success = advancedCalibration->performWhitePointCalibration(20);
      response["success"] = success;
      response["message"] = success ? "White point calibration completed" : "White point calibration failed";
      sendJsonResponse(success ? 200 : 500, response);
    });

    server.on("/advanced-calibration/black", HTTP_POST, []() {
      handleCORSHeaders();
      JsonDocument response;
      bool success = advancedCalibration->performBlackPointCalibration(10);
      response["success"] = success;
      response["message"] = success ? "Black point calibration completed" : "Black point calibration failed";
      sendJsonResponse(success ? 200 : 500, response);
    });

    server.on("/advanced-calibration/complete", HTTP_POST, []() {
      handleCORSHeaders();
      JsonDocument response;
      bool success = advancedCalibration->performCompleteCalibration(true, false);
      response["success"] = success;
      response["message"] = success ? "Complete calibration finished" : "Complete calibration failed";
      if (success) {
        isCalibrated = true;
      }
      sendJsonResponse(success ? 200 : 500, response);
    });

    server.on("/advanced-calibration/status", HTTP_GET, []() {
      handleCORSHeaders();
      JsonDocument response;
      advancedCalibration->getCalibrationStatus(response);
      sendJsonResponse(200, response);
    });
  }

  // Serve static files from LittleFS
  server.onNotFound([]() {
    String path = server.uri();
    if (path.endsWith("/")) path += "index.html";

    String contentType = "text/plain";
    if (path.endsWith(".html")) contentType = "text/html";
    else if (path.endsWith(".css")) contentType = "text/css";
    else if (path.endsWith(".js")) contentType = "application/javascript";
    else if (path.endsWith(".json")) contentType = "application/json";

    if (LittleFS.exists(path)) {
      File file = LittleFS.open(path, "r");
      server.streamFile(file, contentType);
      file.close();
    } else {
      server.send(404, "text/plain", "File not found");
    }
  });

  server.begin();
  LOG_WEB_INFO("Web server started on port %d", WEB_SERVER_PORT);
  LOG_PERF_END("Web server setup");
}

// ============================================================================
// CORS HELPER FUNCTIONS
// ============================================================================

void handleCORSHeaders() {
  server.sendHeader("Access-Control-Allow-Origin", "*");
  server.sendHeader("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS");
  server.sendHeader("Access-Control-Allow-Headers", "Content-Type, Authorization, X-Requested-With");
  server.sendHeader("Access-Control-Max-Age", "86400");
}

void handleCORSPreflight() {
  handleCORSHeaders();
  server.send(200, "text/plain", "");
}

// ============================================================================
// JSON RESPONSE HELPER
// ============================================================================

void sendJsonResponse(int statusCode, const JsonDocument& doc) {
  String responseStr;
  serializeJson(doc, responseStr);
  server.send(statusCode, "application/json", responseStr);
}

// ============================================================================
// LED CONTROL FUNCTIONS
// ============================================================================

void setLEDColor(uint8_t r, uint8_t g, uint8_t b, uint8_t brightness) {
  rgbLed.setPixelColor(0, rgbLed.Color(r, g, b));
  rgbLed.setBrightness(brightness);
  rgbLed.show();
}

void turnOffLED() {
  rgbLed.setPixelColor(0, 0);
  rgbLed.show();
}

void initializePWM() {
  ledcSetup(PWM_CHANNEL, PWM_FREQUENCY, PWM_RESOLUTION);
  ledcAttachPin(ILLUMINATION_LED_PIN, PWM_CHANNEL);
  ledcWrite(PWM_CHANNEL, 0);
}

void setIlluminationBrightness(uint8_t brightness) {
  uint32_t pwmValue = map(brightness, 0, 255, 0, (1 << PWM_RESOLUTION) - 1);
  ledcWrite(PWM_CHANNEL, pwmValue);
  currentBrightness = brightness;
}

void turnOffIllumination() {
  ledcWrite(PWM_CHANNEL, 0);
  currentBrightness = 0;
}

// ============================================================================
// COLOR CONVERSION FUNCTIONS
// ============================================================================

void convertSensorToRGB(uint16_t rawR, uint16_t rawG, uint16_t rawB, uint16_t rawIR, uint8_t& r, uint8_t& g, uint8_t& b) {
  // Simplified color conversion with basic calibration support
  if (advancedCalibration && advancedCalibration->isInitialized()) {
    // Use advanced calibration system for better accuracy
    // For now, use a simple scaling approach that can be enhanced later
    float scale = 255.0f / 65535.0f; // Scale from 16-bit to 8-bit
    r = (uint8_t)constrain(rawR * scale, 0, 255);
    g = (uint8_t)constrain(rawG * scale, 0, 255);
    b = (uint8_t)constrain(rawB * scale, 0, 255);

    LOG_SENSOR_DEBUG("Advanced calibration: Raw(%u,%u,%u) -> RGB(%u,%u,%u)",
                     rawR, rawG, rawB, r, g, b);
  } else {
    // Simple fallback conversion
    float maxRaw = max(max((float)rawR, (float)rawG), (float)rawB);
    if (maxRaw > 0) {
      r = (uint8_t)constrain((rawR / maxRaw) * 255.0f, 0, 255);
      g = (uint8_t)constrain((rawG / maxRaw) * 255.0f, 0, 255);
      b = (uint8_t)constrain((rawB / maxRaw) * 255.0f, 0, 255);
    } else {
      r = g = b = 0;
    }

    LOG_SENSOR_DEBUG("Simple conversion: Raw(%u,%u,%u) Max=%.0f -> RGB(%u,%u,%u)",
                     rawR, rawG, rawB, maxRaw, r, g, b);
  }
}

// ============================================================================
// SETTINGS MANAGEMENT
// ============================================================================

void loadSettings() {
  LOG_PERF_START();

  // Load sensor settings with defaults
  uint16_t atime = preferences.getUInt("atime", DEFAULT_ATIME);
  uint8_t again = preferences.getUInt("again", DEFAULT_AGAIN);
  uint8_t brightness = preferences.getUInt("brightness", DEFAULT_BRIGHTNESS);

  // Apply settings directly to sensor
  tcs3430.setIntegrationTime(atime);
  tcs3430.setALSGain(again);
  currentBrightness = brightness;

  LOG_SYS_INFO("Settings loaded: ATIME=%u AGAIN=%u Brightness=%u", atime, again, brightness);
  LOG_PERF_END("Settings load");
}

void saveSettings() {
  LOG_PERF_START();

  // Save current brightness and any other settings
  preferences.putUInt("brightness", currentBrightness);
  LOG_SYS_INFO("Settings saved: Brightness=%u", currentBrightness);

  LOG_PERF_END("Settings save");
}

void loadSamples() {
  LOG_PERF_START();

  sampleCount = preferences.getUInt("sampleCount", 0);
  sampleIndex = preferences.getUInt("sampleIndex", 0);

  if (sampleCount > MAX_SAMPLES) {
    sampleCount = MAX_SAMPLES;
  }

  int loadedSamples = 0;
  for (int i = 0; i < sampleCount; i++) {
    String key = "sample" + String(i);
    size_t bytesRead = preferences.getBytes(key.c_str(), &samples[i], sizeof(ColorSample));
    if (bytesRead == sizeof(ColorSample)) {
      loadedSamples++;
    }
  }

  LOG_STORAGE_INFO("Sample loading completed - %d/%d samples loaded", loadedSamples, sampleCount);
  LOG_PERF_END("Sample load");
}

void saveSamples() {
  preferences.putUInt("sampleCount", sampleCount);
  preferences.putUInt("sampleIndex", sampleIndex);

  for (int i = 0; i < sampleCount && i < MAX_SAMPLES; i++) {
    String key = "sample" + String(i);
    preferences.putBytes(key.c_str(), &samples[i], sizeof(ColorSample));
  }
}

// ============================================================================
// WEB HANDLER FUNCTIONS
// ============================================================================

void handleScan() {
  LOG_PERF_START();
  LOG_WEB_INFO("Handling scan request");

  if (isScanning) {
    JsonDocument response;
    response["error"] = "Scan already in progress";
    sendJsonResponse(409, response);
    return;
  }

  isScanning = true;

  // Turn on illumination LED
  setIlluminationBrightness(currentBrightness);
  delay(SENSOR_STABILIZE_MS);

  // Read sensor data
  uint16_t rawX = tcs3430.getXData();
  uint16_t rawY = tcs3430.getYData();
  uint16_t rawZ = tcs3430.getZData();
  uint16_t rawIR = tcs3430.getIR1Data();

  // Convert to RGB
  uint8_t r, g, b;
  convertSensorToRGB(rawX, rawY, rawZ, rawIR, r, g, b);

  // Turn off illumination LED
  turnOffIllumination();
  isScanning = false;

  // Prepare response
  JsonDocument response;
  response["r"] = r;
  response["g"] = g;
  response["b"] = b;
  response["raw"]["x"] = rawX;
  response["raw"]["y"] = rawY;
  response["raw"]["z"] = rawZ;
  response["raw"]["ir"] = rawIR;
  response["timestamp"] = millis();

  sendJsonResponse(200, response);
  LOG_PERF_END("Scan");
}

void handleEnhancedScan() {
  LOG_PERF_START();
  LOG_WEB_INFO("Handling enhanced scan request");

  if (isScanning) {
    JsonDocument response;
    response["error"] = "Scan already in progress";
    sendJsonResponse(409, response);
    return;
  }

  isScanning = true;

  // Use advanced calibration for LED brightness optimization if available
  uint8_t optimalBrightness = currentBrightness;
  if (advancedCalibration && advancedCalibration->isInitialized()) {
    optimalBrightness = advancedCalibration->updateLEDBrightnessPID();
  }

  // Turn on illumination LED with optimal brightness
  setIlluminationBrightness(optimalBrightness);
  delay(SENSOR_STABILIZE_MS);

  // Perform multiple readings for better accuracy
  uint32_t sumX = 0, sumY = 0, sumZ = 0, sumIR = 0;
  const int numReadings = 5;

  for (int i = 0; i < numReadings; i++) {
    sumX += tcs3430.getXData();
    sumY += tcs3430.getYData();
    sumZ += tcs3430.getZData();
    sumIR += tcs3430.getIR1Data();
    delay(50);
  }

  uint16_t avgX = sumX / numReadings;
  uint16_t avgY = sumY / numReadings;
  uint16_t avgZ = sumZ / numReadings;
  uint16_t avgIR = sumIR / numReadings;

  // Convert to RGB
  uint8_t r, g, b;
  convertSensorToRGB(avgX, avgY, avgZ, avgIR, r, g, b);

  // Turn off illumination LED
  turnOffIllumination();
  isScanning = false;

  // Prepare enhanced response
  JsonDocument response;
  response["r"] = r;
  response["g"] = g;
  response["b"] = b;
  response["raw"]["x"] = avgX;
  response["raw"]["y"] = avgY;
  response["raw"]["z"] = avgZ;
  response["raw"]["ir"] = avgIR;
  response["enhanced"]["readings"] = numReadings;
  response["enhanced"]["brightness"] = optimalBrightness;
  response["enhanced"]["calibrated"] = (advancedCalibration && advancedCalibration->isInitialized());
  response["timestamp"] = millis();

  sendJsonResponse(200, response);
  LOG_PERF_END("Enhanced scan");
}

void handleSaveSample() {
  LOG_PERF_START();
  LOG_WEB_INFO("Handling save sample request");

  if (!server.hasArg("plain")) {
    JsonDocument response;
    response["error"] = "Missing request body";
    sendJsonResponse(400, response);
    return;
  }

  JsonDocument doc;
  DeserializationError error = deserializeJson(doc, server.arg("plain"));

  if (error) {
    JsonDocument response;
    response["error"] = "Invalid JSON";
    sendJsonResponse(400, response);
    return;
  }

  if (sampleCount >= MAX_SAMPLES) {
    JsonDocument response;
    response["error"] = "Sample storage full";
    sendJsonResponse(507, response);
    return;
  }

  // Extract color values
  uint8_t r = doc["r"] | 0;
  uint8_t g = doc["g"] | 0;
  uint8_t b = doc["b"] | 0;

  // Create new sample
  ColorSample newSample;
  newSample.r = r;
  newSample.g = g;
  newSample.b = b;
  newSample.timestamp = millis();
  strncpy(newSample.paintName, doc["paintName"] | "Unknown", SAMPLE_NAME_LENGTH - 1);
  strncpy(newSample.paintCode, doc["paintCode"] | "", SAMPLE_CODE_LENGTH - 1);
  newSample.lrv = doc["lrv"] | 0.0f;

  // Add to samples array
  samples[sampleCount] = newSample;
  sampleCount++;

  // Save to preferences
  saveSamples();

  JsonDocument response;
  response["success"] = true;
  response["message"] = "Sample saved";
  response["sampleCount"] = sampleCount;

  sendJsonResponse(200, response);
  LOG_PERF_END("Save sample");
}

void handleSavedSamples() {
  LOG_PERF_START();
  LOG_WEB_INFO("Handling saved samples request");

  JsonDocument doc;
  JsonArray samplesArray = doc["samples"].to<JsonArray>();

  for (int i = 0; i < sampleCount; i++) {
    JsonObject sample = samplesArray.add<JsonObject>();
    sample["r"] = samples[i].r;
    sample["g"] = samples[i].g;
    sample["b"] = samples[i].b;
    sample["timestamp"] = samples[i].timestamp;
    sample["paintName"] = samples[i].paintName;
    sample["paintCode"] = samples[i].paintCode;
    sample["lrv"] = samples[i].lrv;
  }

  doc["count"] = sampleCount;
  sendJsonResponse(200, doc);
  LOG_PERF_END("Saved samples");
}

void handleDeleteSample() {
  LOG_PERF_START();
  LOG_WEB_INFO("Handling delete sample request");

  if (!server.hasArg("plain")) {
    JsonDocument response;
    response["error"] = "Missing request body";
    sendJsonResponse(400, response);
    return;
  }

  JsonDocument doc;
  DeserializationError error = deserializeJson(doc, server.arg("plain"));

  if (error) {
    JsonDocument response;
    response["error"] = "Invalid JSON";
    sendJsonResponse(400, response);
    return;
  }

  int index = doc["index"] | -1;
  if (index < 0 || index >= sampleCount) {
    JsonDocument response;
    response["error"] = "Invalid sample index";
    sendJsonResponse(400, response);
    return;
  }

  // Shift samples to remove the specified index
  for (int i = index; i < sampleCount - 1; i++) {
    samples[i] = samples[i + 1];
  }
  sampleCount--;

  // Save updated samples
  saveSamples();

  JsonDocument response;
  response["success"] = true;
  response["message"] = "Sample deleted";
  response["sampleCount"] = sampleCount;

  sendJsonResponse(200, response);
  LOG_PERF_END("Delete sample");
}

void handleClearAllSamples() {
  LOG_PERF_START();
  LOG_WEB_INFO("Handling clear all samples request");

  sampleCount = 0;
  sampleIndex = 0;

  // Clear from preferences
  preferences.putUInt("sampleCount", 0);
  preferences.putUInt("sampleIndex", 0);

  JsonDocument response;
  response["success"] = true;
  response["message"] = "All samples cleared";
  response["sampleCount"] = 0;

  sendJsonResponse(200, response);
  LOG_PERF_END("Clear all samples");
}

void handleSettings() {
  LOG_PERF_START();
  LOG_WEB_INFO("Handling settings update request");

  if (!server.hasArg("plain")) {
    JsonDocument response;
    response["error"] = "Missing request body";
    sendJsonResponse(400, response);
    return;
  }

  JsonDocument doc;
  DeserializationError error = deserializeJson(doc, server.arg("plain"));

  if (error) {
    JsonDocument response;
    response["error"] = "Invalid JSON";
    sendJsonResponse(400, response);
    return;
  }

  // Update brightness if provided
  if (doc["brightness"].is<int>()) {
    uint8_t newBrightness = doc["brightness"];
    if (newBrightness <= 255) {
      currentBrightness = newBrightness;
      preferences.putUInt("brightness", currentBrightness);
      LOG_WEB_INFO("Brightness updated to: %u", currentBrightness);
    }
  }

  // Update sensor settings if provided
  if (doc["atime"].is<int>()) {
    uint16_t newAtime = doc["atime"];
    if (newAtime <= 255) {
      tcs3430.setIntegrationTime(newAtime);
      preferences.putUInt("atime", newAtime);
      LOG_WEB_INFO("ATIME updated to: %u", newAtime);
    }
  }

  if (doc["again"].is<int>()) {
    uint8_t newAgain = doc["again"];
    if (newAgain <= 3) {
      tcs3430.setALSGain(newAgain);
      preferences.putUInt("again", newAgain);
      LOG_WEB_INFO("AGAIN updated to: %u", newAgain);
    }
  }

  JsonDocument response;
  response["success"] = true;
  response["message"] = "Settings updated";

  sendJsonResponse(200, response);
  LOG_PERF_END("Settings update");
}

void handleGetSettings() {
  LOG_PERF_START();
  LOG_WEB_INFO("Handling get settings request");

  JsonDocument response;
  response["brightness"] = currentBrightness;
  response["atime"] = preferences.getUInt("atime", DEFAULT_ATIME);
  response["again"] = preferences.getUInt("again", DEFAULT_AGAIN);
  response["calibrated"] = isCalibrated;
  response["advancedCalibration"] = (advancedCalibration && advancedCalibration->isInitialized());
  response["dynamicSensor"] = (dynamicSensor && dynamicSensor->isInitialized());

  sendJsonResponse(200, response);
  LOG_PERF_END("Get settings");
}

void handleStatus() {
  LOG_PERF_START();
  LOG_WEB_INFO("Handling status request");

  JsonDocument response;
  response["system"]["uptime"] = millis();
  response["system"]["freeHeap"] = ESP.getFreeHeap();
  response["system"]["psramFree"] = ESP.getFreePsram();
  response["system"]["wifiConnected"] = (WiFi.status() == WL_CONNECTED);
  response["system"]["wifiRSSI"] = WiFi.RSSI();

  response["sensor"]["initialized"] = true;
  response["sensor"]["scanning"] = isScanning;
  response["sensor"]["calibrated"] = isCalibrated;

  response["storage"]["sampleCount"] = sampleCount;
  response["storage"]["maxSamples"] = MAX_SAMPLES;

  response["features"]["advancedCalibration"] = (advancedCalibration && advancedCalibration->isInitialized());
  response["features"]["dynamicSensor"] = (dynamicSensor && dynamicSensor->isInitialized());

  sendJsonResponse(200, response);
  LOG_PERF_END("Status");
}

void handleBrightness() {
  LOG_PERF_START();
  LOG_WEB_INFO("Handling brightness control request");

  if (!server.hasArg("plain")) {
    JsonDocument response;
    response["error"] = "Missing request body";
    sendJsonResponse(400, response);
    return;
  }

  JsonDocument doc;
  DeserializationError error = deserializeJson(doc, server.arg("plain"));

  if (error) {
    JsonDocument response;
    response["error"] = "Invalid JSON";
    sendJsonResponse(400, response);
    return;
  }

  uint8_t newBrightness = doc["brightness"] | currentBrightness;
  if (newBrightness <= 255) {
    currentBrightness = newBrightness;
    setIlluminationBrightness(currentBrightness);

    JsonDocument response;
    response["success"] = true;
    response["brightness"] = currentBrightness;
    response["message"] = "Brightness updated";

    sendJsonResponse(200, response);
  } else {
    JsonDocument response;
    response["error"] = "Invalid brightness value";
    sendJsonResponse(400, response);
  }

  LOG_PERF_END("Brightness control");
}

void handleRawSensorData() {
  LOG_PERF_START();
  LOG_WEB_INFO("Handling raw sensor data request");

  // Read current sensor values
  uint16_t rawX = tcs3430.getXData();
  uint16_t rawY = tcs3430.getYData();
  uint16_t rawZ = tcs3430.getZData();
  uint16_t rawIR1 = tcs3430.getIR1Data();
  uint16_t rawIR2 = tcs3430.getIR2Data();

  JsonDocument response;
  response["raw"]["x"] = rawX;
  response["raw"]["y"] = rawY;
  response["raw"]["z"] = rawZ;
  response["raw"]["ir1"] = rawIR1;
  response["raw"]["ir2"] = rawIR2;
  response["raw"]["timestamp"] = millis();

  response["settings"]["brightness"] = currentBrightness;
  response["settings"]["atime"] = preferences.getUInt("atime", DEFAULT_ATIME);
  response["settings"]["again"] = preferences.getUInt("again", DEFAULT_AGAIN);

  // Add calibration status if available
  if (advancedCalibration && advancedCalibration->isInitialized()) {
    JsonDocument calStatus;
    advancedCalibration->getCalibrationStatus(calStatus);
    response["calibration"] = calStatus;
  }

  sendJsonResponse(200, response);
  LOG_PERF_END("Raw sensor data");
}

// ============================================================================
// END OF REFACTORED CODE
// ============================================================================
